-- EssentialMode Garage Database Structure
-- This SQL file creates the necessary table structure for the garage system to work with EssentialMode

-- Create user_vehicle table if it doesn't exist
CREATE TABLE IF NOT EXISTS `user_vehicle` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(50) NOT NULL,
  `vehicle` varchar(50) NOT NULL,
  `plate` varchar(12) NOT NULL,
  `state` int(11) NOT NULL DEFAULT 1,
  `mods` longtext DEFAULT NULL,
  `damage` longtext DEFAULT NULL,
  `fuel` int(11) DEFAULT 100,
  `garage` varchar(50) DEFAULT 'garage_central',
  `impound` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plate` (`plate`),
  KEY `identifier` (`identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add indexes for better performance
ALTER TABLE `user_vehicle` ADD INDEX `idx_identifier` (`identifier`);
ALTER TABLE `user_vehicle` ADD INDEX `idx_plate` (`plate`);
ALTER TABLE `user_vehicle` ADD INDEX `idx_state` (`state`);

-- Example data (optional - remove if not needed)
-- INSERT INTO `user_vehicle` (`identifier`, `vehicle`, `plate`, `state`, `mods`, `damage`, `fuel`, `garage`) VALUES
-- ('steam:110000100000000', 'adder', 'ABC123', 1, '{}', '{}', 100, 'garage_central');
