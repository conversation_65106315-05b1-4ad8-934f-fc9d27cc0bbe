# EssentialMode Garage Setup Guide

## نصب و راه‌اندازی گاراژ برای EssentialMode

### مرحله 1: تنظیمات دیتابیس
1. فایل `essentialmode_garage.sql` را در دیتابیس خود اجرا کنید
2. این فایل جدول `user_vehicle` را ایجاد می‌کند

### مرحله 2: تنظیمات فریمورک
در فایل `config.lua`:
```lua
Customize.Framework = "EssentialMode"
```

### مرحله 3: وابستگی‌ها
مطمئن شوید که این ریسورس‌ها در سرور شما نصب و فعال هستند:
- `essentialmode`
- `mysql-async` یا `ghmattimysql` یا `oxmysql`

### مرحله 4: ساختار دیتابیس
جدول `owned_vehicles` شامل این ستون‌ها است:
- `id`: شناسه یکتا
- `owner`: شناسه بازیکن (Steam ID)
- `vehicle`: نام ماشین
- `plate`: پلاک ماشین
- `state`: وضعیت ماشین (1 = در گاراژ، 0 = خارج از گاراژ)
- `mods`: تنظیمات ماشین (JSON)
- `damage`: آسیب‌های ماشین (JSON)
- `fuel`: سوخت ماشین
- `garage`: نام گاراژ
- `impound`: آیا ماشین توقیف شده (0 = خیر، 1 = بله)

### مرحله 5: تست
1. سرور را راه‌اندازی کنید
2. به یکی از گاراژ‌ها بروید
3. با کلید E گاراژ را باز کنید

### نکات مهم:
- EssentialMode از سیستم Event استفاده می‌کند نه Callback
- دیتابیس EssentialMode متفاوت از ESX است
- مطمئن شوید که جدول `owned_vehicles` درست ایجاد شده باشد

### عیب‌یابی:
اگر مشکلی داشتید:
1. Console سرور را چک کنید
2. مطمئن شوید که EssentialMode درست نصب شده
3. دیتابیس کانکشن را بررسی کنید
4. جدول `owned_vehicles` را چک کنید

### تفاوت‌های اصلی با ESX:
- جدول: `owned_vehicles` (مثل ESX)
- ستون شناسه: `owner` (مثل ESX)
- سیستم: Event-based به جای Callback-based
