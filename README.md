# [FiveM Garage Script](https://youtu.be/ZyGHSdPcUAQ)

Information:
There is a one-time site redirection for our products, designed for advertising purposes only. Please note, this is not a virus; it is simply an href transfer.

[![YouTube Subscribe](https://img.shields.io/badge/YouTube-Subscribe-red?style=for-the-badge&logo=youtube)](https://youtu.be/ZyGHSdPcUAQ)
[![Discord](https://img.shields.io/badge/Discord-Join-blue?style=for-the-badge&logo=discord)](https://discord.gg/EkwWvFS)
[![Tebex Store](https://img.shields.io/badge/Tebex-Store-green?style=for-the-badge&logo=shopify)](https://eyestore.tebex.io/)

SETUP Easy:
ALTER TABLE `player_vehicles`
ADD COLUMN `damage` TEXT DEFAULT NULL;

**FiveM Garage Script** is a comprehensive solution that allows players to manage their vehicles in a structured and immersive way. This script is compatible with both QBCore and ESX frameworks, providing a robust and seamless integration.

![FiveM Garage Script Preview](https://github.com/user-attachments/assets/3f1f1b19-94ac-4ddc-8f8b-381e09abb30d)

## Features
- **Vehicle Management**: Manage a fleet of vehicles with ease, including storage and retrieval options.
- **Framework Compatibility**: Works seamlessly with QBCore and ESX frameworks.

## Tebex Store
Explore premium features and support our development by visiting our Tebex store:
[![Tebex](https://img.shields.io/badge/Tebex-EYE%20STORE-00A2FF.svg)](https://eyestore.tebex.io/)

## Discord Community
Join our Discord community for real-time support and regular updates:
[![Discord](https://img.shields.io/badge/Discord-ES%20Community-7289DA.svg)](https://discord.gg/EkwWvFS)

## Contributors
- **Raider#0101**

You can check out the YouTube video tutorial and showcase of the script by clicking the link below:
[![FiveM Garage Script](https://img.shields.io/badge/YouTube-FiveM%20Garage%20Script-FF0000.svg)](https://youtu.be/ZyGHSdPcUAQ)
